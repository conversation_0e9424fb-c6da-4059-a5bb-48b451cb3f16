import 'dart:developer';
import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:web/web.dart' as web;
import 'app_routes.dart';

/// Navigation Service - Clean and URL-Aware
class AppNavigationService {
  final GlobalKey<NavigatorState> navigatorKey = rootNavigatorKey;

  NavigatorState? get navigator => navigatorKey.currentState;

  BuildContext? get context => navigatorKey.currentContext;

  /// Log navigation action with URL update
  void _logNavigation(String action, String route) {
    if (kDebugMode) {
      log('🚀 Navigation: $action to $route');
      if (kIsWeb) {

        log('🌐 URL updated: ${web.window.location.href}');
      }
    }
  }

  /// Push new route
  Future<void> navigateTo(String route, {Object? arguments}) async {
    if (context == null) return;

    try {
      _logNavigation('PUSH', route);
      context!.push(route, extra: arguments);
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Replace current route
  Future<void> replaceWith(String route, {Object? arguments}) async {
    if (context == null) return;

    try {
      _logNavigation('REPLACE', route);
      context!.pushReplacement(route, extra: arguments);
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Clear stack and navigate
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    if (context == null) return;

    try {
      _logNavigation('GO', route);
      context!.go(route, extra: arguments);
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Go back
  void goBack() {
    try {
      if (context != null && context!.canPop()) {
        _logNavigation('POP', 'previous route');
        context!.pop();
      }
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Navigate to dashboard with current user
  Future<void> navigateToDashboard() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.dashboardWithUser(userEmail);
      await clearAndNavigateTo(route);
    } else {
      await clearAndNavigateTo(RouteNames.login);
    }
  }

  /// Navigate to dashboard with current user (alias for compatibility)
  Future<void> navigateToDashboardWithUser() async {
    await navigateToDashboard();
  }

  /// Navigate to portfolio with current user
  Future<void> navigateToPortfolio() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final route = AppRoutes.portfolioWithUser(userEmail);
      await navigateTo(route);
    } else {
      await navigateTo(RouteNames.portfolio);
    }
  }

  /// Navigate to portfolio with current user (alias for compatibility)
  Future<void> navigateToPortfolioWithUser() async {
    await navigateToPortfolio();
  }

  /// Navigate to portfolio by username
  Future<void> navigateToPortfolioByUsername(String username) async {
    final route = AppRoutes.portfolioByUsername(username);
    await navigateTo(route);
  }

}
