import 'dart:developer';

import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/material.dart';
import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Core/services/user_verification_service.dart';

// Global navigator key for GoRouter
final GlobalKey<NavigatorState> rootNavigatorKey = GlobalKey<NavigatorState>();

/// Simple Routes System - Clean and Direct
class AppRoutes {
  /// Build dashboard route with user email
  static String dashboardWithUser(String userEmail) {
    final username = userEmail.split('@').first;
    return '/dashboard/$username';
  }

  /// Build portfolio route with user email
  static String portfolioWithUser(String userEmail) {
    final username = userEmail.split('@').first;
    return '/portfolio/$username';
  }

  /// Build portfolio route with username directly
  static String portfolioByUsername(String username) {
    return '/portfolio/$username';
  }

  /// Extract username from route
  static String? extractUsernameFromRoute(String route) {
    if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
      final parts = route.split('/');
      if (parts.length >= 3) return parts[2];
    }
    // Handle direct username routes (like /felopaters37)
    if (route.startsWith('/') && route.length > 1) {
      final parts = route.split('/');
      if (parts.length == 2 && parts[1].isNotEmpty) {
        final username = parts[1];
        // Check if it's not a static route
        if (![
          'login',
          'register',
          'dashboard',
          'portfolio',
        ].contains(username.toLowerCase())) {
          return username;
        }
      }
    }
    return null;
  }

  /// Check if user is logged in
  static bool isUserLoggedIn() {
    log("message");
    final userData = UserDataService.getUserData();
    return userData != null && userData['emailUser'] != null;
  }

  /// Get current user email
  static String? getCurrentUserEmail() {
    final userData = UserDataService.getUserData();
    return userData?['emailUser'] as String?;
  }

  /// Check if username belongs to current user
  static Future<bool> isCurrentUser(String username) async {
    // final currentEmail = getCurrentUserEmail();
    // log("currentEmail :: $currentEmail");
    // if (currentEmail != null) {
    //   final currentUsername = currentEmail.split('@').first;
    //   final check = username.toLowerCase() == currentUsername.toLowerCase();
    //   log("check :: $check");
    //   if (check) {
    //     return true;
    //   }
    // }
    final isFoundInDatabase = await UserVerificationService.checkUserByUsername(
      username,
    );
    isFoundInDatabase.fold((error) => false, (portfolioData) async{
      log("portfolioData :: $portfolioData");
     await UserDataService.saveUserData(portfolioData!.toJson());
    });
    log("isFoundInDatabase :: $isFoundInDatabase");
    log("isRight :: ${isFoundInDatabase.isRight()}");
    log("isLeft :: ${isFoundInDatabase.isLeft()}");
    return isFoundInDatabase.isRight();
  }

  /// Get initial route based on authentication
  static String getInitialRoute() {
    if (isUserLoggedIn()) {
      final userEmail = getCurrentUserEmail()!;
      return dashboardWithUser(userEmail);
    }
    return RouteNames.login; // Start with login if not logged in
  }
}
