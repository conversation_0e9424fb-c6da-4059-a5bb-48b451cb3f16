class EducationModel {
  final String? id;
  final String? degree;
  final String? institution;
  final String? startDate;
  final String? endDate;
  final String? description;
  final String? location;
  final String? gpa;

  EducationModel({
    this.id,
    this.degree,
    this.institution,
    this.startDate,
    this.endDate,
    this.description,
    this.location,
    this.gpa,
  });

  factory EducationModel.fromJson(Map<String, dynamic> json) {
    return EducationModel(
      id: json['id'],
      degree: json['degree'],
      institution: json['institution'],
      startDate: json['startDate'],
      endDate: json['endDate'],
      description: json['description'],
      location: json['location'],
      gpa: json['gpa'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (degree != null) 'degree': degree,
      if (institution != null) 'institution': institution,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (description != null) 'description': description,
      if (location != null) 'location': location,
      if (gpa != null) 'gpa': gpa,
    };
  }
}
