import 'dart:developer';

import 'package:devfolio/Core/Utils/widgets/build_loading.dart';
import 'package:devfolio/Core/models/portfolio_data_model.dart';
import 'package:devfolio/Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/login_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/register_page.dart';
import 'package:devfolio/Features/Portfolio/portfolio_main.dart';
import 'package:devfolio/config/Routes/app_routes.dart';
import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../Core/Storage/Local/UserDataService/user_data_service.dart';

/// GoRouter Configuration - Clean and Optimized
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: RouteNames.splash,
    navigatorKey: rootNavigatorKey,
    routes: [
      // Root route - redirects based on authentication
      GoRoute(
        path: RouteNames.splash,
        name: RouteNames.splashName,
        builder: (context, state) =>
            Scaffold(body: Center(child: buildLoading())),
      ),

      // Static routes
      GoRoute(
        path: RouteNames.login,
        name: RouteNames.loginName,
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: RouteNames.register,
        name: RouteNames.registerName,
        builder: (context, state) => const RegisterPage(),
      ),

      // Dynamic routes with username parameter
      GoRoute(
        path: RouteNames.dashboardWithUser,
        name: RouteNames.dashboardWithUserName,
        builder: (context, state) {
          // final username = state.pathParameters['username']!;
          // if (AppRoutes.isCurrentUser(username)) {
          //   return const AdminDashboard();
          // } else {
          //   return const LoginPage();
          // }
          return const AdminDashboard();
        },
      ),

      // Direct username routes
      GoRoute(
        path: RouteNames.directUsername,
        name: RouteNames.directUsernameName,
        builder: (context, state) {
          final username = state.pathParameters['username']!;
          final getData = UserDataService.getUserData();
          PortfolioDataModel? data;
          if (getData != null) {
            data = PortfolioDataModel.fromJson(getData);
          }
          log(data!.email.toString());
          log(username);
          return PortfolioMain(portfolioData: data);
        },
        redirect: (context, state) async {
          log(
            "Current path: ${(state.fullPath)?.split(r"/").last.toString()} ",
          );
          final isCurrentUser = await AppRoutes.isCurrentUser(
            (state.matchedLocation).split("/:").toString(),
          );
          if (isCurrentUser) {
            return RouteNames.directUsername;
          } else {
            return RouteNames.splashName;
          }
        },
      ),
    ],
    // Error handling - fallback to login
    errorBuilder: (context, state) => const LoginPage(),

    redirect: (context, state) async {
      if (state.matchedLocation == RouteNames.splash) {
        return AppRoutes.getInitialRoute();
      }

      return null;
    },
  );
}
