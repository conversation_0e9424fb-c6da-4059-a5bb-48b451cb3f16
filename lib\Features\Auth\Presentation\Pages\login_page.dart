import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../Core/Utils/widgets/animated_background.dart';
import '../../../../Core/Utils/widgets/message_widget.dart';
import '../../../../Core/layout/responsive_layout.dart';
import '../../../../main.dart';
import '../../Data/Cubit/auth_cubit.dart';
import '../../Data/Cubit/auth_state.dart';
import '../Widgets/PartsLogin/build_auth_card.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController(
    text: '<EMAIL>',
  );
  final _passwordController = TextEditingController(text: 'Felopater123');

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: BlocListener<AuthCubit, AuthState>(
          listener: (context, state) {
            if (state.isSuccess == true) {
              MessageWidget.show(
                context,
                type: MessageType.success,
                message: state.message ?? 'Login successful',
              );
              // Navigate to dashboard with user email in URL
              kNavigationService.navigateToDashboardWithUser();
            } else if (state.isSuccess == false) {
              MessageWidget.show(
                context,
                type: MessageType.error,
                message: state.message ?? 'Login failed',
              );
            }
          },
          child: ResponsiveLayout.isMobile(context)
              ? _buildMobileLayout()
              : _buildLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return CustomScrollView(
      physics: const RangeMaintainingScrollPhysics(),
      slivers: [
        SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          sliver: SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: true,
            child: _buildLayout(),
          ),
        ),
      ],
    );
  }

  Widget _buildLayout() {
    return Center(
      child: BuildAuthCard(
        context: context,
        formKey: _formKey,
        emailController: _emailController,
        passwordController: _passwordController,
      ).animate().fadeIn(duration: 800.ms).scale(begin: const Offset(0.9, 0.9)),
    );
  }
}
