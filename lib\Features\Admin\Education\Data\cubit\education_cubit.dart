import 'package:flutter_bloc/flutter_bloc.dart';
import '../Model/education.dart';

// Education State
class EducationState {
  final List<Education> educationList;
  final Education? editingEducation;
  final bool isAddingNew;
  final bool isLoading;
  final String? errorMessage;
  final bool hasChanges;

  EducationState({
    this.educationList = const [],
    this.editingEducation,
    this.isAddingNew = false,
    this.isLoading = false,
    this.errorMessage,
    this.hasChanges = false,
  });

  EducationState copyWith({
    List<Education>? educationList,
    Education? editingEducation,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? hasChanges,
  }) {
    return EducationState(
      educationList: educationList ?? this.educationList,
      editingEducation: editingEducation ?? this.editingEducation,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      hasChanges: hasChanges ?? this.hasChanges,
    );
  }
}

// Education Cubit
class EducationCubit extends Cubit<EducationState> {
  EducationCubit() : super(EducationState());

  // Load education data
  Future<void> loadEducation() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      // Mock data - replace with actual API call
      final List<Education> educationList = [
        Education(
          id: '1',
          name: 'Bachelor of Computer Science',
          part: 'Computer Science',
          startDate: '2018-09',
          endDate: '2022-06',
          type: 'education',
          description:
              'Studied computer science with focus on software development and mobile applications.',
          location: 'University Name, City, Country',
        ),
        Education(
          id: '2',
          name: 'Flutter Development Certification',
          part: 'Mobile Development',
          startDate: '2021-03',
          endDate: '2021-06',
          type: 'certification',
          description:
              'Completed comprehensive Flutter development course covering advanced topics.',
          location: 'Online Course Platform',
        ),
        Education(
          id: '3',
          name: 'Senior Flutter Developer',
          part: 'Tech Company',
          startDate: '2023-01',
          endDate: 'Present',
          type: 'experience',
          description:
              'Led development of multiple Flutter applications, mentored junior developers, and implemented best practices.',
          location: 'Remote',
          technologies: ['Flutter', 'Dart', 'Firebase', 'Git'],
        ),
        Education(
          id: '4',
          name: 'Flutter Developer',
          part: 'Startup Inc',
          startDate: '2022-06',
          endDate: '2022-12',
          type: 'experience',
          description:
              'Developed and maintained mobile applications using Flutter framework.',
          location: 'New York',
          technologies: ['Flutter', 'Dart', 'REST APIs'],
        ),
        Education(
          id: '5',
          name: 'Master of Software Engineering',
          part: 'Software Engineering',
          startDate: '2022-09',
          endDate: '2024-06',
          type: 'education',
          description:
              'Advanced studies in software engineering and development methodologies.',
          location: 'University Name, City, Country',
        ),
      ];

      emit(
        state.copyWith(
          educationList: educationList,
          isLoading: false,
          hasChanges: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to load education data: $e',
        ),
      );
    }
  }

  // Add new education
  void addNewEducation() {
    emit(
      state.copyWith(
        editingEducation: null,
        isAddingNew: true,
        errorMessage: null,
      ),
    );
  }

  // Edit education
  void editEducation(Education education) {
    emit(
      state.copyWith(
        editingEducation: education,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Cancel edit
  void cancelEdit() {
    emit(
      state.copyWith(
        editingEducation: null,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Save education
  Future<void> saveEducation({
    required String name,
    required String part,
    required String startDate,
    required String endDate,
    required String type,
    String? description,
    String? location,
    List<String>? technologies,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 1000));

      final newEducation = Education(
        id:
            state.editingEducation?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        part: part,
        startDate: startDate,
        endDate: endDate,
        type: type,
        description: description,
        location: location,
        technologies: technologies,
      );

      List<Education> updatedList = List.from(state.educationList);

      if (state.isAddingNew) {
        updatedList.add(newEducation);
      } else {
        final index = updatedList.indexWhere((e) => e.id == newEducation.id);
        if (index != -1) {
          updatedList[index] = newEducation;
        }
      }

      emit(
        state.copyWith(
          educationList: updatedList,
          editingEducation: null,
          isAddingNew: false,
          isLoading: false,
          hasChanges: true,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to save education: $e',
        ),
      );
    }
  }

  // Delete education
  Future<void> deleteEducation(Education education) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedList = state.educationList
          .where((e) => e.id != education.id)
          .toList();

      emit(
        state.copyWith(
          educationList: updatedList,
          editingEducation: state.editingEducation?.id == education.id
              ? null
              : state.editingEducation,
          isAddingNew: state.editingEducation?.id == education.id
              ? false
              : state.isAddingNew,
          isLoading: false,
          hasChanges: true,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to delete education: $e',
        ),
      );
    }
  }

  // Clear error
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  // Reset state
  void reset() {
    emit(EducationState());
  }
}
