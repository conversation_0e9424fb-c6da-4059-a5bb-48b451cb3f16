import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../Features/Auth/Data/auth_source.dart';
import '../../Core/models/portfolio_data_model.dart';

// Portfolio State
class PortfolioState {
  final int currentSection;
  final bool isLoading;
  final PortfolioDataModel? portfolioData;

  PortfolioState({
    this.currentSection = 0,
    this.isLoading = false,
    this.portfolioData,
  });

  PortfolioState copyWith({
    int? currentSection,
    bool? isLoading,
    PortfolioDataModel? portfolioData,
  }) {
    return PortfolioState(
      currentSection: currentSection ?? this.currentSection,
      isLoading: isLoading ?? this.isLoading,
      portfolioData: portfolioData ?? this.portfolioData,
    );
  }
}

// Portfolio Cubit
class PortfolioCubit extends Cubit<PortfolioState> {
  PortfolioCubit() : super(PortfolioState());

  void initPortfolioData({
    PortfolioDataModel? portfolioData,
    String? email,
  }) async {
    if (portfolioData == null) {
      final initialState = await AuthSource.getPortfolioData(email!);
      initialState.fold((error) {
        log('error: $error');
      }, (data) => emit(state.copyWith(portfolioData: data)));
    } else if (email == null) {
      emit(state.copyWith(portfolioData: portfolioData));
    } else {
      return;
    }
  }

  void setCurrentSection(int section) {
    emit(state.copyWith(currentSection: section));
  }

  void setLoading(bool loading) {
    emit(state.copyWith(isLoading: loading));
  }

  void nextSection() {
    final nextSection = (state.currentSection + 1) % 6;
    emit(state.copyWith(currentSection: nextSection));
  }

  void previousSection() {
    final previousSection = (state.currentSection - 1 + 6) % 6;
    emit(state.copyWith(currentSection: previousSection));
  }
}
