import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationFormFields extends StatelessWidget {
  final TextEditingController nameController;
  final TextEditingController partController;
  final TextEditingController startDateController;
  final TextEditingController endDateController;
  final TextEditingController typeController;
  final TextEditingController descriptionController;
  final TextEditingController locationController;
  final String selectedType;
  final List<String> partsForType;
  final Function(String?) onTypeChanged;

  const EducationFormFields({
    super.key,
    required this.nameController,
    required this.partController,
    required this.startDateController,
    required this.endDateController,
    required this.typeController,
    required this.descriptionController,
    required this.locationController,
    required this.selectedType,
    required this.partsForType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Education Name
        _buildFormField(
          controller: nameController,
          label: AppStrings.educationName,
          icon: Icons.school,
          hint: AppStrings.enterEducationName,
        ),
        SizedBox(height: 15.h),

        // Type Dropdown
        _buildDropdownField(
          controller: typeController,
          label: AppStrings.type,
          icon: Icons.category,
          items: ['education', 'experience', 'certification'],
          hint: AppStrings.selectType,
          value: selectedType,
          onChanged: onTypeChanged,
        ),
        SizedBox(height: 15.h),

        // Part Dropdown
        _buildDropdownField(
          controller: partController,
          label: AppStrings.part,
          icon: Icons.category,
          items: partsForType,
          hint: AppStrings.selectPart,
          value: partsForType.contains(partController.text)
              ? partController.text
              : null,
          onChanged: (String? newValue) {
            if (newValue != null) {
              partController.text = newValue;
            }
          },
        ),
        SizedBox(height: 15.h),

        // Start Date
        _buildFormField(
          controller: startDateController,
          label: AppStrings.startDate,
          icon: Icons.calendar_today,
          hint: 'YYYY-MM',
          keyboardType: TextInputType.datetime,
        ),
        SizedBox(height: 15.h),

        // End Date
        _buildFormField(
          controller: endDateController,
          label: AppStrings.endDate,
          icon: Icons.calendar_today,
          hint: 'YYYY-MM or Present',
          keyboardType: TextInputType.datetime,
        ),
        SizedBox(height: 15.h),

        // Description
        _buildFormField(
          controller: descriptionController,
          label: 'Description',
          icon: Icons.description,
          hint: 'Enter description (optional)',
          maxLines: 3,
        ),
        SizedBox(height: 15.h),

        // Location
        _buildFormField(
          controller: locationController,
          label: 'Location',
          icon: Icons.location_on,
          hint: 'Enter location (optional)',
        ),
      ],
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hint,
    TextInputType? keyboardType,
    int? maxLines,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 18.sp),
              SizedBox(width: 8.w),
              CustomText(
                text: label,
                fontSize: ResponsiveLayout.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines ?? 1,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.6),
                fontSize: ResponsiveLayout.getBodyFontSize(context),
              ),
              filled: true,
              fillColor: AppColors.cardSurface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 10.h,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required List<String> items,
    required String hint,
    String? value,
    required Function(String?) onChanged,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 18.sp),
              SizedBox(width: 8.w),
              CustomText(
                text: label,
                fontSize: ResponsiveLayout.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          DropdownButtonFormField<String>(
            value: value,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.6),
                fontSize: ResponsiveLayout.getBodyFontSize(context),
              ),
              filled: true,
              fillColor: AppColors.cardSurface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(
                  color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 10.h,
              ),
            ),
            items: items.map((String item) {
              return DropdownMenuItem<String>(value: item, child: Text(item));
            }).toList(),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}
