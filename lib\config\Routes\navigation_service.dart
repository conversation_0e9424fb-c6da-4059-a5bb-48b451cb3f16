import 'dart:developer';
import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:web/web.dart' as web;
import 'app_routes.dart';

/// Navigation Service - Clean and URL-Aware for go_router 16.0.0
class AppNavigationService {
  final GlobalKey<NavigatorState> navigatorKey = rootNavigatorKey;

  NavigatorState? get navigator => navigatorKey.currentState;

  BuildContext? get context => navigatorKey.currentContext;
  List<String> navigationStack = [];

  /// Log navigation action with URL update
  void _logNavigation(String action, String route) {

    if (kDebugMode) {
      log('🚀 Navigation: $action to $route');
      // Add a small delay to ensure URL is updated before logging
      Future.delayed(const Duration(milliseconds: 100), () {
        if (kIsWeb) {
          log('🌐 URL updated: ${web.window.location.href}');
        }
      });
    }
  }

  /// Push new route - Updates URL in browser


  /// Replace current route - Updates URL in browser
  Future<void> replaceWith(String route, {Object? arguments}) async {
    if (context == null) {
      log('❌ Navigation context is null');
      return;
    }

    try {
      _logNavigation('REPLACE', route);
      context!.pushReplacement(route, extra: arguments);
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Clear stack and navigate - Updates URL in browser
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    if (context == null) {
      log('❌ Navigation context is null');
      return;
    }

    try {
      _logNavigation('GO', route);
      context!.go(route, extra: arguments);
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Go back
  void goBack() {
    try {
      if (context != null && context!.canPop()) {
        _logNavigation('POP', 'previous route');
        context!.pop();
      }
    } catch (e) {
      log('❌ Navigation error: $e');
    }
  }

  /// Navigate using named route - Better URL handling
  Future<void> navigateToNamed(
    String routeName, {
    Map<String, String>? pathParameters,
    Object? extra,
  }) async {
    if (context == null) {
      log('❌ Navigation context is null');
      return;
    }

    try {
      _logNavigation('PUSH_NAMED', routeName);
      await context!.pushNamed(
        routeName,
        pathParameters: pathParameters ?? {},
        extra: extra,
      );
    } catch (e) {
      log('❌ Named navigation error: $e');
    }
  }

  /// Replace with named route - Better URL handling
  Future<void> replaceWithNamed(
    String routeName, {
    Map<String, String>? pathParameters,
    Object? extra,
  }) async {
    if (context == null) {
      log('❌ Navigation context is null');
      return;
    }

    try {
      navigationStack.removeLast();
      navigationStack.add(routeName);
      _logNavigation('REPLACE_NAMED', routeName);
      context!.pushReplacementNamed(
        routeName,
        pathParameters: pathParameters ?? {},
        extra: extra,
      );
    } catch (e) {
      log('❌ Named navigation error: $e');
    }
  }

  /// Go to named route - Better URL handling
  Future<void> goToNamed(
    String routeName, {
    Map<String, String>? pathParameters,
    Object? extra,
  }) async {
    if (context == null) {
      log('❌ Navigation context is null');
      return;
    }

    try {
      navigationStack.add(routeName);
      _logNavigation('GO_NAMED', routeName);

      context!.goNamed(
        routeName,
        pathParameters: pathParameters ?? {},
        extra: extra,
      );
    } catch (e) {
      log('❌ Named navigation error: $e');
    }
  }

  /// Navigate to dashboard with current user - Modern approach using named routes
  Future<void> navigateToDashboard() async {
    final userEmail = AppRoutes.getCurrentUserEmail();
    if (userEmail != null) {
      final username = userEmail.split('@').first;
      await goToNamed(
        RouteNames.dashboardWithUserName,
        pathParameters: {'username': username},
      );
    } else {
      await goToNamed(RouteNames.loginName);
    }
  }

  /// Navigate to dashboard with current user (alias for compatibility)
  Future<void> navigateToDashboardWithUser() async {
    await navigateToDashboard();
  }

  /// Navigate to portfolio with current user - Modern approach using named routes
  Future<void> navigateToPortfolio() async {
    // final userEmail = AppRoutes.getCurrentUserEmail();
    // if (userEmail != null) {
    //   final username = userEmail.split('@').first;
    //   await navigateToNamed(
    //     RouteNames.portfolioWithUserName,
    //     pathParameters: {'username': username},
    //   );
    // } else {
    //   await navigateToNamed(RouteNames.portfolioName);
    // }
  }



  /// Navigate to portfolio by username - Modern approach using named routes
  // Future<void> navigateToPortfolioByUsername(String username) async {
  //   await navigateToNamed(
  //     RouteNames.portfolioWithUserName,
  //     pathParameters: {'username': username},
  //   );
  // }

}
