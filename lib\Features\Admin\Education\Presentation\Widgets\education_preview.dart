import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationPreview extends StatelessWidget {
  final String name;
  final String part;
  final String startDate;
  final String endDate;
  final String type;

  const EducationPreview({
    super.key,
    required this.name,
    required this.part,
    required this.startDate,
    required this.endDate,
    required this.type,
  });

  IconData _getIconForType(String type) {
    switch (type) {
      case 'education':
        return Icons.school;
      case 'experience':
        return Icons.work;
      case 'certification':
        return Icons.verified;
      default:
        return Icons.school;
    }
  }

  String _formatDate(String date) {
    if (date.isEmpty) return 'Not specified';
    if (date.toLowerCase() == 'present') return 'Present';

    try {
      final parts = date.split('-');
      if (parts.length == 2) {
        final year = parts[0];
        final month = parts[1];
        final monthNames = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];
        final monthIndex = int.parse(month) - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
          return '${monthNames[monthIndex]} $year';
        }
      }
      return date;
    } catch (e) {
      return date;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
        color: AppColors.withOpacity(AppColors.primary, 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.primary, 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: AppStrings.preview,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.withOpacity(AppColors.primary, 0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Icon(
                  _getIconForType(type),
                  color: AppColors.primary,
                  size: 18.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: name.isNotEmpty ? name : AppStrings.educationName,
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      fontWeight: FontWeight.w600,
                    ),
                    CustomText(
                      text: part.isNotEmpty ? part : AppStrings.part,
                      fontSize: ResponsiveLayout.getSmallFontSize(context),
                      color: AppColors.textSecondary,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(Icons.calendar_today, color: AppColors.primary, size: 14.sp),
              SizedBox(width: 4.w),
              CustomText(
                text: '${_formatDate(startDate)} - ${_formatDate(endDate)}',
                fontSize: 12.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
