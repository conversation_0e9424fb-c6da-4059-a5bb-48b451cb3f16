import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Utils/Reusable/custom_button.dart';
import '../../../../../Core/Utils/Reusable/custom_text.dart';
import '../../../../../Core/Utils/widgets/build_text_field.dart';
import '../../../../../Core/Utils/widgets/password_text__form_field.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

import '../../../../../config/Routes/route_names.dart';
import '../../../../../main.dart';
import '../../../Data/Cubit/auth_cubit.dart';
import '../../../Data/Models/auth_model.dart';
import '../auth_header.dart';

class BuildAuthCardRegister extends StatelessWidget {
  const BuildAuthCardRegister({
    super.key,
    required this.context,
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController emailController,
    required TextEditingController phoneController,
    required TextEditingController passwordController,
  }) : _formKey = formKey,
       _nameController = nameController,
       _emailController = emailController,
       _phoneController = phoneController,
       _passwordController = passwordController;

  final BuildContext context;
  final GlobalKey<FormState> _formKey;
  final TextEditingController _nameController;
  final TextEditingController _emailController;
  final TextEditingController _phoneController;
  final TextEditingController _passwordController;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ResponsiveLayout.isMobile(context) ? double.infinity : 450.w,
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          AuthHeader(
            title: 'Create Account',
            subtitle: 'Sign up to get started with your portfolio',
            icon: Icons.person_add,
          ),
          SizedBox(height: 32.h),

          // Form
          Form(
            key: _formKey,
            child: Column(
              children: [
                // Name Field
                BuildTextField(
                  controller: _nameController,
                  label: 'Full Name',
                  icon: Icons.person_outline,
                  keyboardType: TextInputType.name,
                  hint: 'Enter your full name',
                ),
                SizedBox(height: 20.h),

                // Email Field
                BuildTextField(
                  controller: _emailController,
                  label: 'Email',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  hint: 'Enter your email',
                ),
                SizedBox(height: 20.h),

                // Phone Field
                BuildTextField(
                  controller: _phoneController,
                  label: 'Phone Number',
                  icon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  hint: 'Enter your phone number',
                ),
                SizedBox(height: 20.h),

                // Password Field
                CustomPasswordTextFromField(
                  controller: _passwordController,
                  fieldId: 'password',
                  hintText: 'Enter your password',
                  isLogin: false,
                ),
                SizedBox(height: 8.h),

                SizedBox(height: 24.h),

                // Register Button
                CustomButton(
                  text: 'Create Account',
                  onPressed: _handleRegister,
                  isLoading: context.watch<AuthCubit>().state.isLoading,
                ),
                SizedBox(height: 20.h),

                // Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(
                      text: 'Already have an account? ',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: AppColors.textSecondary,
                    ),
                    GestureDetector(
                      onTap: () {
                        context.read<AuthCubit>().clearState();
                        kNavigationService.goBack();
                      },
                      child: CustomText(
                        text: 'Sign In',
                        fontSize: ResponsiveLayout.getBodyFontSize(context),
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleRegister() {
    if (_formKey.currentState!.validate()) {
      final registerModel = AuthModel(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        phone: _phoneController.text.trim(),
      );
      context.read<AuthCubit>().register(registerModel);
    }
  }
}
