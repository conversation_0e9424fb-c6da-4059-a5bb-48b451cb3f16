/// Application constants for dimensions, durations, and other values
class AppConstants {
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  static const Duration veryLongAnimation = Duration(milliseconds: 800);

  // API Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration shortTimeout = Duration(seconds: 10);

  // Debounce Delays
  static const Duration searchDebounce = Duration(milliseconds: 300);
  static const Duration saveDebounce = Duration(milliseconds: 500);

  // Loading Delays
  static const Duration minLoadingTime = Duration(milliseconds: 500);
  static const Duration maxLoadingTime = Duration(seconds: 10);

  // Snackbar Durations
  static const Duration shortSnackbar = Duration(seconds: 2);
  static const Duration mediumSnackbar = Duration(seconds: 4);
  static const Duration longSnackbar = Duration(seconds: 6);

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;

  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 100;

  // File Sizes (in bytes)
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDocumentSize = 10 * 1024 * 1024; // 10MB

  // Cache Durations
  static const Duration shortCache = Duration(minutes: 5);
  static const Duration mediumCache = Duration(hours: 1);
  static const Duration longCache = Duration(days: 1);

  // Retry Attempts
  static const int maxRetryAttempts = 3;
  static const int maxApiRetries = 2;

  // Validation Patterns
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phonePattern = r'^\+?[\d\s\-\(\)]+$';
  static const String urlPattern = r'^https?:\/\/[\w\-\.]+\.\w+';

  // Default Values
  static const String defaultAvatar = 'assets/images/default_avatar.png';
  static const String defaultProjectImage = 'assets/images/default_project.png';

  // Error Codes
  static const String networkError = 'NETWORK_ERROR';
  static const String serverError = 'SERVER_ERROR';
  static const String validationError = 'VALIDATION_ERROR';
  static const String authError = 'AUTH_ERROR';
  static const String unknownError = 'UNKNOWN_ERROR';

  // Success Codes
  static const String successCode = 'SUCCESS';
  static const String createdCode = 'CREATED';
  static const String updatedCode = 'UPDATED';
  static const String deletedCode = 'DELETED';

  // Status Codes
  static const int okStatus = 200;
  static const int createdStatus = 201;
  static const int badRequestStatus = 400;
  static const int unauthorizedStatus = 401;
  static const int forbiddenStatus = 403;
  static const int notFoundStatus = 404;
  static const int serverErrorStatus = 500;

  // Admin Section Indices
  static const int personalInfoIndex = 1;
  static const int dashboardIndex = 0;
  static const int skillsIndex = 2;
  static const int projectsIndex = 3;
  static const int educationIndex = 4;
  static const int contactIndex = 5;

  // Form Field Names
  static const String nameField = 'name';
  static const String emailField = 'email';
  static const String phoneField = 'phone';
  static const String titleField = 'title';
  static const String locationField = 'location';
  static const String aboutField = 'about';
  static const String messageField = 'message';
  static const String subjectField = 'subject';

  // Storage Keys
  static const String userPrefsKey = 'user_preferences';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';

  // API Endpoints (if needed)
  static const String baseUrl = 'https://api.example.com';
  static const String authEndpoint = '/auth';
  static const String userEndpoint = '/user';
  static const String portfolioEndpoint = '/portfolio';

  // Feature Flags
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enableAutoSave = true;
  static const bool enableOfflineMode = false;

  // Limits
  static const int maxSkillsPerCategory = 10;
  static const int maxProjects = 20;
  static const int maxExperienceEntries = 10;
  static const int maxEducationEntries = 5;

  // Default Counts
  static const int defaultSkillsCount = 1;
  static const int defaultProjectsCount = 1;
  static const int defaultExperienceCount = 1;
  static const int defaultEducationCount = 1;
}
