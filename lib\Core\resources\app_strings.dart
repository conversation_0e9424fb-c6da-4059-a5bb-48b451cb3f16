/// Application string constants
class AppStrings {
  // App Information
  static const String appName = 'DevFolio';
  static const String appTitle = 'Portfolio';
  static const String appSubtitle = 'Developer Portfolio';

  // Navigation
  static const String home = 'Home';
  static const String about = 'About';
  static const String skills = 'Skills';
  static const String experience = 'Experience';
  static const String projects = 'Projects';
  static const String education = 'Education';
  static const String contact = 'Contact';
  static const String dashboard = 'Dashboard';

  // Admin Panel
  static const String adminPanel = 'Admin Panel';
  static const String portfolioManager = 'Portfolio Manager';
  static const String portfolioManagement = 'Portfolio Management';
  static const String viewPortfolio = 'View Portfolio';
  static const String saveChanges = 'Save Changes';
  static const String allSaved = 'All Saved';
  static const String saving = 'Saving...';
  static const String unsavedChanges = 'Unsaved Changes';
  static const String dataSavedSuccessfully = 'Data saved successfully!';
  static const String exportFunctionality =
      'Export functionality - Coming Soon';

  // Admin Sections
  static const String personalInfo = 'Personal & Contact';
  static const String personalInfoTooltip =
      'Manage your personal information and contact details';
  static const String skillsTooltip = 'Edit your skills and expertise';
  static const String experienceTooltip = 'Manage work experience';
  static const String projectsTooltip = 'Add and edit projects';
  static const String educationTooltip = 'Update education details';
  static const String contactTooltip = 'Edit contact information';

  // Personal Information
  static const String fullName = 'Full Name';
  static const String professionalTitle = 'Professional Title';
  static const String emailAddress = 'Email Address';
  static const String phoneNumber = 'Phone Number';
  static const String location = 'Location';
  static const String aboutMeSection = 'About Me';
  static const String personalInformation = 'Personal Information';
  static const String updatePersonalDetails =
      'Update your personal details and professional information';
  static const String personalInfoSaved =
      'Personal information saved successfully!';
  static const String formReset = 'Form reset to default values';

  // Form Labels
  static const String enterFullName = 'Enter your full name';
  static const String flutterDeveloperExample =
      'e.g., Flutter Developer, UI/UX Designer';
  static const String emailExample = '<EMAIL>';
  static const String phoneExample = '+1234567890';
  static const String locationExample = 'City, Country';
  static const String aboutMeHint =
      'Tell people about yourself, your passion, and what you do...';
  static const String resetForm = 'Reset Form';

  // Skills
  static const String programmingLanguages = 'Programming Languages';
  static const String frameworksLibraries = 'Frameworks & Libraries';
  static const String databases = 'Databases';
  static const String toolsPlatforms = 'Tools & Platforms';
  static const String softSkills = 'Soft Skills';
  static const String keySkills = 'Key Skills';

  // Skills List
  static const String dart = 'Dart';
  static const String javascript = 'JavaScript';
  static const String python = 'Python';
  static const String java = 'Java';
  static const String flutter = 'Flutter';
  static const String react = 'React';
  static const String nodejs = 'Node.js';
  static const String expressjs = 'Express.js';
  static const String firebase = 'Firebase';
  static const String mongodb = 'MongoDB';
  static const String sqlite = 'SQLite';
  static const String postgresql = 'PostgreSQL';
  static const String git = 'Git';
  static const String docker = 'Docker';
  static const String aws = 'AWS';
  static const String heroku = 'Heroku';
  static const String teamLeadership = 'Team Leadership';
  static const String problemSolving = 'Problem Solving';
  static const String communication = 'Communication';
  static const String agile = 'Agile';

  // Experience
  static const String techSolutionsInc = 'Tech Solutions Inc.';
  static const String mobileAppsCo = 'Mobile Apps Co.';
  static const String startupVentures = 'Startup Ventures';
  static const String seniorFlutterDeveloper = 'Senior Flutter Developer';
  static const String flutterDeveloper = 'Flutter Developer';
  static const String juniorDeveloper = 'Junior Developer';
  static const String present = 'Present';
  static const String duration2022Present = '2022 - Present';
  static const String duration20202022 = '2020 - 2022';
  static const String duration20192020 = '2019 - 2020';

  // Projects
  static const String featuredProject = 'Featured Project';
  static const String otherProjects = 'Other Projects';
  static const String ecommerceApp = 'E-Commerce Mobile App';
  static const String taskManagementApp = 'Task Management App';
  static const String weatherApp = 'Weather App';
  static const String socialMediaApp = 'Social Media App';
  static const String fitnessTracker = 'Fitness Tracker';
  static const String newsReader = 'News Reader';
  static const String budgetManager = 'Budget Manager';

  // Project Descriptions
  static const String ecommerceDescription =
      'A full-featured e-commerce application with user authentication, product catalog, shopping cart, and payment integration.';
  static const String taskManagementDescription =
      'A productivity app for managing tasks and projects with team collaboration features.';
  static const String weatherDescription =
      'Real-time weather application with location-based forecasts and beautiful UI.';
  static const String socialMediaDescription =
      'A social networking app with user profiles, posts, and real-time messaging.';
  static const String fitnessDescription =
      'Health and fitness tracking app with workout plans and progress monitoring.';
  static const String newsDescription =
      'News aggregation app with multiple sources and personalized content.';
  static const String budgetDescription =
      'Personal finance app for tracking expenses and managing budgets.';

  // Technologies
  static const String stripe = 'Stripe';
  static const String bloc = 'BLoC';
  static const String provider = 'Provider';
  static const String openWeatherApi = 'OpenWeather API';
  static const String geolocation = 'Geolocation';
  static const String webrtc = 'WebRTC';
  static const String healthApis = 'Health APIs';
  static const String charts = 'Charts';
  static const String restApis = 'REST APIs';
  static const String caching = 'Caching';

  // About
  static const String aboutMeTitle = 'About Me';
  static const String aboutDescription =
      'I am a passionate Flutter developer with over 3 years of experience in mobile app development. I specialize in creating cross-platform applications that provide excellent user experiences.';
  static const String mobileDevelopment = 'Mobile Development';
  static const String mobileDevelopmentDesc =
      'Expert in Flutter and Dart for cross-platform mobile development';
  static const String uiuxDesign = 'UI/UX Design';
  static const String uiuxDesignDesc =
      'Creating beautiful and intuitive user interfaces with modern design principles';
  static const String problemSolvingDesc =
      'Strong analytical skills to solve complex technical challenges';

  // Home
  static const String johnDoe = 'John Doe';
  static const String flutterDeveloperTitle = 'Flutter Developer';
  static const String homeDescription =
      'Passionate about creating beautiful and functional mobile applications with Flutter. Experienced in cross-platform development and modern UI/UX design.';
  static const String viewProjects = 'View Projects';
  static const String contactMe = 'Contact Me';

  // Contact
  static const String getInTouch = 'Get In Touch';
  static const String contactDescription =
      'Feel free to reach out for collaborations or just a friendly hello';
  static const String email = 'Email';
  static const String phone = 'Phone';
  static const String address = 'Address';
  static const String sendMessage = 'Send Message';
  static const String name = 'Name';
  static const String subject = 'Subject';
  static const String message = 'Message';
  static const String enterName = 'Enter your name';
  static const String enterSubject = 'Enter subject';
  static const String enterMessage = 'Enter your message';

  // Validation Messages
  static const String requiredField = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email address';
  static const String invalidPhone = 'Please enter a valid phone number';

  // Success Messages
  static const String success = 'Success';
  static const String operationCompleted = 'Operation completed successfully';

  // Error Messages
  static const String error = 'Error';
  static const String somethingWentWrong = 'Something went wrong';
  static const String tryAgain = 'Please try again';

  // Loading Messages
  static const String loading = 'Loading...';
  static const String pleaseWait = 'Please wait...';

  // Common Actions
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String save = 'Save';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String update = 'Update';
  static const String select = 'Select';

  // Selection Messages
  static const String selectEducationToEdit =
      'Select an education item to edit';
  static const String selectSkillToEdit = 'Select a skill to edit';
  static const String selectProjectToEdit = 'Select a project to edit';

  // Project Actions
  static const String deleteProject = 'Delete Project';
  static const String deleteProjectConfirmation =
      'Are you sure you want to delete';
  static const String actionCannotBeUndone = 'This action cannot be undone.';
  static const String addNewProject = 'Add Project';
  static const String manageProjectsDescription =
      'Add, edit, and manage your portfolio projects';

  // Skill Categories
  static const String framework = 'Framework';
  static const String language = 'Language';
  static const String backend = 'Backend';
  static const String database = 'Database';
  static const String versionControl = 'Version Control';
  static const String design = 'Design';
  static const String testing = 'Testing';
  static const String devops = 'DevOps';
  static const String other = 'Other';

  // Skill Icons
  static const String flutterIcon = 'flutter';
  static const String codeIcon = 'code';
  static const String cloudIcon = 'cloud';
  static const String gitIcon = 'git';
  static const String apiIcon = 'api';
  static const String designIcon = 'design';
  static const String databaseIcon = 'database';
  static const String testIcon = 'test';
  static const String deployIcon = 'deploy';
  static const String mobileIcon = 'mobile';
  static const String webIcon = 'web';
  static const String serverIcon = 'server';
  static const String securityIcon = 'security';
  static const String analyticsIcon = 'analytics';
  static const String aiIcon = 'ai';

  // Default Values
  static const String defaultName = 'Your Name';
  static const String defaultTitle = 'Flutter Developer';
  static const String defaultEmail = '<EMAIL>';
  static const String defaultPhone = '+1234567890';
  static const String defaultLocation = 'City, Country';
  static const String defaultAbout =
      'I\'m a passionate Flutter developer with experience in building beautiful, responsive web and mobile applications.';

  // Admin Stats
  static const String personalInfoCount = '1';
  static const String skillsCount = '1';
  static const String projectsCount = '1';
  static const String experienceCount = '1';

  // Welcome Messages
  static const String welcomeToAdminPanel = 'Welcome to Admin Panel';
  static const String managePortfolioContent =
      'Manage your portfolio content easily';
  static const String selectSectionToEdit =
      'Select a section from the sidebar to start editing your portfolio content. All changes will be saved automatically.';

  // Coming Soon Messages
  static const String comingSoon = 'Coming Soon';
  static const String skillsEditorComingSoon = 'Skills Editor - Coming Soon';
  static const String experienceEditorComingSoon =
      'Experience Editor - Coming Soon';
  static const String educationEditorComingSoon =
      'Education Editor - Coming Soon';
  static const String contactEditorComingSoon = 'Contact Editor - Coming Soon';

  // Personal & Contact Editor
  static const String personalContactInfo = 'Personal & Contact Information';
  static const String managePersonalContact =
      'Manage your personal details and contact information';
  static const String basicInformation = 'Basic Information';
  static const String aboutMe = 'About Me';
  static const String contactDetails = 'Contact Details';
  static const String professionalLinks = 'Professional Links';
  static const String socialMediaProfiles = 'Social Media Profiles';
  static const String additionalInformation = 'Additional Information';
  static const String shortDescription = 'Short Description';
  static const String detailedBio = 'Detailed Bio';
  static const String website = 'Website';
  static const String linkedin = 'LinkedIn';
  static const String github = 'GitHub';
  static const String twitter = 'Twitter/X';
  static const String instagram = 'Instagram';
  static const String facebook = 'Facebook';
  static const String youtube = 'YouTube';
  static const String dateOfBirth = 'Date of Birth';
  static const String nationality = 'Nationality';
  static const String languages = 'Languages';
  static const String interestsHobbies = 'Interests & Hobbies';

  // Skills Editor
  static const String skillsManagement = 'Skills Management';
  static const String manageSkillsDescription =
      'Add, edit, and manage your technical skills and expertise';
  static const String addNewSkill = 'Add New Skill';
  static const String editSkill = 'Edit Skill';
  static const String addSkill = 'Add Skill';
  static const String updateSkill = 'Update Skill';
  static const String deleteSkill = 'Delete Skill';
  static const String deleteSkillConfirmation =
      'Are you sure you want to delete the skill';
  static const String skillDeleted = 'Skill deleted';
  static const String skillAdded = 'Skill added successfully';
  static const String skillUpdated = 'Skill updated successfully';
  static const String skillName = 'Skill Name';
  static const String enterSkillName = 'Enter skill name';
  static const String category = 'Category';
  static const String selectCategory = 'Select category';
  static const String icon = 'Icon';
  static const String preview = 'Preview';
  static const String noSkillsYet = 'No Skills Yet';
  static const String addFirstSkill =
      'Start by adding your first skill to showcase your expertise';

  // Education Editor
  static const String educationManagement = 'Education Management';
  static const String manageEducationDescription =
      'Add, edit, and manage your educational background';
  static const String addNewEducation = 'Add New Education';
  static const String editEducation = 'Edit Education';
  static const String addEducation = 'Add Education';
  static const String updateEducation = 'Update Education';
  static const String deleteEducation = 'Delete Education';
  static const String deleteEducationConfirmation =
      'Are you sure you want to delete the education';
  static const String educationDeleted = 'Education deleted';
  static const String educationAdded = 'Education added successfully';
  static const String educationUpdated = 'Education updated successfully';
  static const String educationName = 'Education Name';
  static const String enterEducationName = 'Enter education name';
  static const String part = 'Part';
  static const String selectPart = 'Select part';
  static const String startDate = 'Start Date';
  static const String endDate = 'End Date';
  static const String type = 'Type';
  static const String selectType = 'Select type';
  static const String noEducationYet = 'No Education Yet';
  static const String addFirstEducation =
      'Start by adding your first education entry';

  // Dashboard
  static const String quickStats = 'Quick Stats';
  static const String recentActivity = 'Recent Activity';
  static const String errorOccurred = 'Error Occurred';
  static const String retry = 'Retry';
}
