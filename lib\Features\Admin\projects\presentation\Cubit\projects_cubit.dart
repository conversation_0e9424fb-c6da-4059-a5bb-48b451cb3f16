import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/project_model.dart';
import '../../../../../Core/services/data_service.dart';
import 'projects_state.dart';

class ProjectsCubit extends Cubit<ProjectsState> {
  ProjectsCubit() : super(ProjectsState());

  // Load projects data
  Future<void> loadProjects() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    
    try {
      final projectsData = await DataService.getProjects();
      final List<ProjectModel> projectsList = projectsData
          .map((data) => ProjectModel.fromJson(data))
          .toList();

      emit(
        state.copyWith(
          projectsList: projectsList,
          isLoading: false,
          hasChanges: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to load projects data: $e',
        ),
      );
    }
  }

  // Add new project
  void addNewProject() {
    emit(
      state.copyWith(
        editingProject: null,
        isAddingNew: true,
        errorMessage: null,
      ),
    );
  }

  // Edit project
  void editProject(ProjectModel project) {
    emit(
      state.copyWith(
        editingProject: project,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Cancel edit
  void cancelEdit() {
    emit(
      state.copyWith(
        editingProject: null,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Save project
  Future<void> saveProject({
    required String? title,
    required String? description,
    required String? image,
    required String? githubUrl,
    required String? liveUrl,
    required String? category,
    required List<String>? technologies,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final projectData = {
        'title': title ?? '',
        'description': description ?? '',
        'image': image ?? '',
        'github_url': githubUrl ?? '',
        'live_url': liveUrl ?? '',
        'category': category ?? '',
        'technologies': technologies ?? [],
      };

      bool success;
      if (state.isAddingNew) {
        success = await DataService.addProject(projectData);
      } else {
        success = await DataService.updateProject(
          state.editingProject!.id ?? '',
          projectData,
        );
      }

      if (success) {
        await loadProjects(); // Reload data
        emit(
          state.copyWith(
            editingProject: null,
            isAddingNew: false,
            isLoading: false,
            hasChanges: true,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to save project',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to save project: $e',
        ),
      );
    }
  }

  // Delete project
  Future<void> deleteProject(ProjectModel project) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final success = await DataService.deleteProject(project.id!);

      if (success) {
        await loadProjects(); // Reload data
        emit(
          state.copyWith(
            editingProject: state.editingProject?.id == project.id
                ? null
                : state.editingProject,
            isAddingNew: state.editingProject?.id == project.id
                ? false
                : state.isAddingNew,
            isLoading: false,
            hasChanges: true,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to delete project',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to delete project: $e',
        ),
      );
    }
  }

  // Clear error
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  // Reset state
  void reset() {
    emit(ProjectsState());
  }
}
