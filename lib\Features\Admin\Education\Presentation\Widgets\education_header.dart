import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationHeader extends StatelessWidget {
  final VoidCallback onAddNew;

  const EducationHeader({super.key, required this.onAddNew});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ResponsiveLayout.getContainerPadding(context),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.withOpacity(AppColors.primary, 0.1),
            AppColors.withOpacity(AppColors.primaryVariant, 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.primary, 0.2),
        ),
      ),
      child: ResponsiveLayout.isMobile(context)
          ? _buildMobileHeader(context)
          : _buildDesktopHeader(context),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.1);
  }

  Widget _buildMobileHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(10.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.school,
                color: AppColors.textPrimary,
                size: ResponsiveLayout.getIconSize(context),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: AppStrings.educationManagement,
                    fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                    fontWeight: FontWeight.bold,
                  ),
                  CustomText(
                    text: AppStrings.manageEducationDescription,
                    fontSize: ResponsiveLayout.getSmallFontSize(context),
                    color: AppColors.textSecondary,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 15.h),
        SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: AppStrings.addNewEducation,
            onPressed: onAddNew,
            backgroundColor: AppColors.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Icon(
            Icons.school,
            color: AppColors.textPrimary,
            size: ResponsiveLayout.getLargeIconSize(context),
          ),
        ),
        SizedBox(width: 15.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: AppStrings.educationManagement,
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                fontWeight: FontWeight.bold,
              ),
              CustomText(
                text: AppStrings.manageEducationDescription,
                fontSize: ResponsiveLayout.getSmallFontSize(context),
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
        CustomButton(
          text: AppStrings.addNewEducation,
          onPressed: onAddNew,
          backgroundColor: AppColors.primary,
        ),
      ],
    );
  }
}
