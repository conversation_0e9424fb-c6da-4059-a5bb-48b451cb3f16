import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../Data/Model/education.dart';

class EducationCard extends StatelessWidget {
  final Education education;
  final bool isSelected;
  final Function(Education) onEdit;
  final Function(Education) onDelete;

  const EducationCard({
    super.key,
    required this.education,
    required this.isSelected,
    required this.onEdit,
    required this.onDelete,
  });

  IconData _getIconForType(String type) {
    switch (type) {
      case 'education':
        return Icons.school;
      case 'experience':
        return Icons.work;
      case 'certification':
        return Icons.verified;
      default:
        return Icons.school;
    }
  }

  String _formatDate(String date) {
    if (date.isEmpty) return 'Not specified';
    if (date.toLowerCase() == 'present') return 'Present';

    try {
      final parts = date.split('-');
      if (parts.length == 2) {
        final year = parts[0];
        final month = parts[1];
        final monthNames = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];
        final monthIndex = int.parse(month) - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
          return '${monthNames[monthIndex]} $year';
        }
      }
      return date;
    } catch (e) {
      return date;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        bottom: ResponsiveLayout.isMobile(context) ? 8.h : 10.h,
      ),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.withOpacity(AppColors.primary, 0.1)
            : AppColors.cardSurface,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: isSelected
              ? AppColors.primary
              : AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: ResponsiveLayout.isMobile(context)
          ? _buildMobileCard(context)
          : _buildDesktopCard(context),
    );
  }

  Widget _buildMobileCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.withOpacity(AppColors.primary, 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getIconForType(education.type),
                  color: AppColors.primary,
                  size: ResponsiveLayout.getIconSize(context),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: education.name,
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      fontWeight: FontWeight.w600,
                    ),
                    SizedBox(height: 4.h),
                    CustomText(
                      text: education.part,
                      fontSize: ResponsiveLayout.getSmallFontSize(context),
                      color: AppColors.textSecondary,
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, color: AppColors.textSecondary),
                onSelected: (value) {
                  if (value == 'edit') {
                    onEdit(education);
                  } else if (value == 'delete') {
                    onDelete(education);
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, color: AppColors.primary, size: 18.sp),
                        SizedBox(width: 8.w),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: AppColors.error, size: 18.sp),
                        SizedBox(width: 8.w),
                        Text('Delete'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(Icons.calendar_today, color: AppColors.primary, size: 14.sp),
              SizedBox(width: 4.w),
              CustomText(
                text:
                    '${_formatDate(education.startDate)} - ${_formatDate(education.endDate)}',
                fontSize: 12.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopCard(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: AppColors.withOpacity(AppColors.primary, 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          _getIconForType(education.type),
          color: AppColors.primary,
          size: 20.sp,
        ),
      ),
      title: CustomText(
        text: education.name,
        fontSize: ResponsiveLayout.getBodyFontSize(context),
        fontWeight: FontWeight.w600,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 4.h),
          CustomText(
            text: education.part,
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 6.h),
          Row(
            children: [
              Icon(Icons.calendar_today, color: AppColors.primary, size: 14.sp),
              SizedBox(width: 4.w),
              CustomText(
                text:
                    '${_formatDate(education.startDate)} - ${_formatDate(education.endDate)}',
                fontSize: 12.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: Icon(Icons.edit, color: AppColors.primary, size: 20.sp),
            onPressed: () => onEdit(education),
          ),
          IconButton(
            icon: Icon(Icons.delete, color: AppColors.error, size: 20.sp),
            onPressed: () => onDelete(education),
          ),
        ],
      ),
      onTap: () => onEdit(education),
    );
  }
}
