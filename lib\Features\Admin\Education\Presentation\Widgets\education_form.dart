import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../Data/Model/education.dart';
import 'education_form_fields.dart';
import 'education_preview.dart';

class EducationForm extends StatefulWidget {
  final Education? editingEducation;
  final bool isAddingNew;
  final Function({
    required String name,
    required String part,
    required String startDate,
    required String endDate,
    required String type,
    String? description,
    String? location,
    List<String>? technologies,
  })
  onSave;
  final VoidCallback onCancel;

  const EducationForm({
    super.key,
    this.editingEducation,
    required this.isAddingNew,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<EducationForm> createState() => _EducationFormState();
}

class _EducationFormState extends State<EducationForm> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _partController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _typeController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();

  String _selectedType = 'education';

  final List<String> _educationParts = [
    'Computer Science',
    'Software Engineering',
    'Information Technology',
    'Data Science',
    'Artificial Intelligence',
    'Cybersecurity',
    'Network Engineering',
    'Database Management',
    'Cloud Computing',
    'DevOps',
    'UI/UX Design',
    'Game Development',
    'Other',
  ];

  final List<String> _experienceParts = [
    'Tech Company',
    'Startup',
    'Freelance',
    'Consulting',
    'Enterprise',
    'Agency',
    'Remote',
    'Other',
  ];

  final List<String> _certificationParts = [
    'Mobile Development',
    'Web Development',
    'Cloud Computing',
    'Data Science',
    'Cybersecurity',
    'DevOps',
    'UI/UX Design',
    'Project Management',
    'Agile/Scrum',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(EducationForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.editingEducation != widget.editingEducation ||
        oldWidget.isAddingNew != widget.isAddingNew) {
      _initializeControllers();
    }
  }

  void _initializeControllers() {
    _nameController.text = widget.editingEducation?.name ?? '';
    _partController.text = widget.editingEducation?.part ?? '';
    _startDateController.text = widget.editingEducation?.startDate ?? '';
    _endDateController.text = widget.editingEducation?.endDate ?? '';
    _typeController.text = widget.editingEducation?.type ?? 'education';
    _descriptionController.text = widget.editingEducation?.description ?? '';
    _locationController.text = widget.editingEducation?.location ?? '';
    _selectedType = widget.editingEducation?.type ?? 'education';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _partController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _typeController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  List<String> _getPartsForType(String type) {
    switch (type) {
      case 'education':
        return _educationParts;
      case 'experience':
        return _experienceParts;
      case 'certification':
        return _certificationParts;
      default:
        return _educationParts;
    }
  }

  void _onTypeChanged(String? newType) {
    if (newType != null) {
      setState(() {
        _selectedType = newType;
        _typeController.text = newType;
        // Reset part when type changes
        if (!_getPartsForType(newType).contains(_partController.text)) {
          _partController.clear();
        }
      });
    }
  }

  void _onSave() {
    widget.onSave(
      name: _nameController.text,
      part: _partController.text,
      startDate: _startDateController.text,
      endDate: _endDateController.text,
      type: _selectedType,
      description: _descriptionController.text.isNotEmpty
          ? _descriptionController.text
          : null,
      location: _locationController.text.isNotEmpty
          ? _locationController.text
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: ResponsiveLayout.getSmallContainerPadding(context),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.primary, 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  widget.isAddingNew ? Icons.add : Icons.edit,
                  color: AppColors.primary,
                  size: ResponsiveLayout.getIconSize(context),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomText(
                    text: widget.isAddingNew
                        ? AppStrings.addNewEducation
                        : AppStrings.editEducation,
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: ResponsiveLayout.getSmallContainerPadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  EducationFormFields(
                    nameController: _nameController,
                    partController: _partController,
                    startDateController: _startDateController,
                    endDateController: _endDateController,
                    typeController: _typeController,
                    descriptionController: _descriptionController,
                    locationController: _locationController,
                    selectedType: _selectedType,
                    partsForType: _getPartsForType(_selectedType),
                    onTypeChanged: _onTypeChanged,
                  ),
                  SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),
                  if (widget.editingEducation != null || widget.isAddingNew)
                    EducationPreview(
                      name: _nameController.text,
                      part: _partController.text,
                      startDate: _startDateController.text,
                      endDate: _endDateController.text,
                      type: _selectedType,
                    ),
                ],
              ),
            ),
          ),
          Container(
            padding: ResponsiveLayout.getSmallContainerPadding(context),
            child: ResponsiveLayout.isMobile(context)
                ? _buildMobileButtons(context)
                : _buildDesktopButtons(context),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: CustomButton(
            text: widget.isAddingNew
                ? AppStrings.addEducation
                : AppStrings.updateEducation,
            onPressed: _onSave,
            backgroundColor: AppColors.primary,
          ),
        ),
        if (!widget.isAddingNew) ...[
          SizedBox(height: 10.h),
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: AppStrings.cancel,
              onPressed: widget.onCancel,
              backgroundColor: AppColors.transparent,
              textColor: AppColors.primary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDesktopButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: widget.isAddingNew
                ? AppStrings.addEducation
                : AppStrings.updateEducation,
            onPressed: _onSave,
            backgroundColor: AppColors.primary,
          ),
        ),
        if (!widget.isAddingNew) ...[
          SizedBox(width: 10.w),
          Expanded(
            child: CustomButton(
              text: AppStrings.cancel,
              onPressed: widget.onCancel,
              backgroundColor: AppColors.transparent,
              textColor: AppColors.primary,
            ),
          ),
        ],
      ],
    );
  }
}
