import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../Data/cubit/education_cubit.dart';
import '../../Data/Model/education.dart';
import '../Widgets/education_header.dart';
import '../Widgets/education_list.dart';
import '../Widgets/education_form.dart';

class EducationEditorPage extends StatelessWidget {
  const EducationEditorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EducationCubit()..loadEducation(),
      child: BlocConsumer<EducationCubit, EducationState>(
        listener: (context, state) {
          // Handle error messages
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.error, color: AppColors.textPrimary),
                    SizedBox(width: 10.w),
                    Expanded(child: Text(state.errorMessage!)),
                  ],
                ),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
            );
            context.read<EducationCubit>().clearError();
          }

          // Handle success messages
          if (state.hasChanges && !state.isLoading) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: AppColors.textPrimary),
                    SizedBox(width: 10.w),
                    Text(
                      state.isAddingNew
                          ? AppStrings.educationAdded
                          : AppStrings.educationUpdated,
                    ),
                  ],
                ),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          return Container(
            padding: ResponsiveLayout.getContainerPadding(context),
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.circular(15.r),
              border: Border.all(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.withOpacity(AppColors.shadowDark, 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                EducationHeader(
                  onAddNew: () =>
                      context.read<EducationCubit>().addNewEducation(),
                ),
                SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
                Expanded(child: _buildResponsiveContent(context, state)),
              ],
            ),
          ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.1);
        },
      ),
    );
  }

  Widget _buildResponsiveContent(BuildContext context, EducationState state) {
    if (ResponsiveLayout.isMobile(context)) {
      // Mobile: Stack layout with form overlay
      return _buildMobileLayout(context, state);
    } else {
      // Desktop: Side-by-side layout
      return _buildDesktopLayout(context, state);
    }
  }

  Widget _buildMobileLayout(BuildContext context, EducationState state) {
    return Column(
      children: [
        // Education List (takes most space on mobile)
        Expanded(flex: 2, child: _buildEducationList(context, state)),
        SizedBox(height: 15.h),
        // Form section (smaller on mobile)
        if (state.isAddingNew || state.editingEducation != null)
          Expanded(flex: 1, child: _buildEducationForm(context, state)),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context, EducationState state) {
    return Row(
      children: [
        // Education List
        Expanded(flex: 2, child: _buildEducationList(context, state)),
        SizedBox(width: 20.w),
        // Add/Edit Form
        Expanded(flex: 1, child: _buildEducationForm(context, state)),
      ],
    );
  }

  Widget _buildEducationList(BuildContext context, EducationState state) {
    return EducationList(
      educationList: state.educationList,
      selectedEducation: state.editingEducation,
      onEdit: (education) =>
          context.read<EducationCubit>().editEducation(education),
      onDelete: (education) => _showDeleteDialog(context, education),
    );
  }

  Widget _buildEducationForm(BuildContext context, EducationState state) {
    if (!state.isAddingNew && state.editingEducation == null) {
      return Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.edit_note,
                size: ResponsiveLayout.isMobile(context) ? 40.sp : 60.sp,
                color: AppColors.textSecondary,
              ),
              SizedBox(height: 15.h),
              CustomText(
                text: AppStrings.selectEducationToEdit,
                fontSize: ResponsiveLayout.getBodyFontSize(context),
                color: AppColors.textSecondary,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return EducationForm(
      editingEducation: state.editingEducation,
      isAddingNew: state.isAddingNew,
      onSave:
          ({
            required String name,
            required String part,
            required String startDate,
            required String endDate,
            required String type,
            String? description,
            String? location,
            List<String>? technologies,
          }) => _handleSaveEducation(
            context,
            name: name,
            part: part,
            startDate: startDate,
            endDate: endDate,
            type: type,
            description: description,
            location: location,
            technologies: technologies,
          ),
      onCancel: () => context.read<EducationCubit>().cancelEdit(),
    );
  }

  void _handleSaveEducation(
    BuildContext context, {
    required String name,
    required String part,
    required String startDate,
    required String endDate,
    required String type,
    String? description,
    String? location,
    List<String>? technologies,
  }) {
    context.read<EducationCubit>().saveEducation(
      name: name,
      part: part,
      startDate: startDate,
      endDate: endDate,
      type: type,
      description: description,
      location: location,
      technologies: technologies,
    );
  }

  void _showDeleteDialog(BuildContext context, Education education) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        title: CustomText(
          text: AppStrings.deleteEducation,
          fontSize: ResponsiveLayout.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
        ),
        content: CustomText(
          text:
              '${AppStrings.deleteEducationConfirmation} "${education.name}"?',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: CustomText(
              text: AppStrings.cancel,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.textSecondary,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<EducationCubit>().deleteEducation(education);
            },
            child: CustomText(
              text: AppStrings.delete,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }
}
