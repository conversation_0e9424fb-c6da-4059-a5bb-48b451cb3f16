class ExperienceModel {
  final String? id;
  final String? company;
  final String? position;
  final String? startDate;
  final String? endDate;
  final String? description;
  final List<String>? technologies;
  final String? location;

  ExperienceModel({
    this.id,
    this.company,
    this.position,
    this.startDate,
    this.endDate,
    this.description,
    this.technologies,
    this.location,
  });

  factory ExperienceModel.fromJson(Map<String, dynamic> json) {
    return ExperienceModel(
      id: json['id'],
      company: json['company'],
      position: json['position'],
      startDate: json['startDate'],
      endDate: json['endDate'],
      description: json['description'],
      technologies: (json['technologies'] as List?)
          ?.map((e) => e.toString())
          .toList(),
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (company != null) 'company': company,
      if (position != null) 'position': position,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (description != null) 'description': description,
      if (technologies != null) 'technologies': technologies,
      if (location != null) 'location': location,
    };
  }
}
