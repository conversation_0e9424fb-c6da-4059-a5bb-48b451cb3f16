import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../Data/Model/education.dart';
import 'education_card.dart';
import 'education_empty_state.dart';

class EducationList extends StatelessWidget {
  final List<Education> educationList;
  final Education? selectedEducation;
  final Function(Education) onEdit;
  final Function(Education) onDelete;

  const EducationList({
    super.key,
    required this.educationList,
    this.selectedEducation,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: ResponsiveLayout.getSmallContainerPadding(context),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.primary, 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.list,
                  color: AppColors.primary,
                  size: ResponsiveLayout.getIconSize(context),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomText(
                    text: ResponsiveLayout.isMobile(context)
                        ? 'Education (${educationList.length})'
                        : 'Education, Experience & Certifications (${educationList.length})',
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: educationList.isEmpty
                ? const EducationEmptyState()
                : ListView.builder(
                    padding: ResponsiveLayout.getSmallContainerPadding(context),
                    itemCount: educationList.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: ResponsiveLayout.isMobile(context)
                              ? 8.h
                              : 12.h,
                        ),
                        child: EducationCard(
                          education: educationList[index],
                          isSelected:
                              selectedEducation?.id == educationList[index].id,
                          onEdit: onEdit,
                          onDelete: onDelete,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
