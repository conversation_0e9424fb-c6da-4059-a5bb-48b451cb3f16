import 'package:devfolio/config/Routes/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'Core/resources/resources.dart';
import 'Features/Auth/Data/Cubit/auth_cubit.dart';
import 'config/Routes/index.dart';
import 'config/Themes/themes_app.dart';
import 'config/cubit/admin_cubit.dart';

class AuthApp extends StatelessWidget {
  const AuthApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1920, 1080),
      minTextAdapt: true,
      splitScreenMode: true,
      useInheritedMediaQuery: true,
      enableScaleWH: () => false,
      enableScaleText: () => true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
            BlocProvider<AdminCubit>(create: (context) => AdminCubit()),
          ],
          child: MaterialApp.router(
            title: AppStrings.appTitle,
            debugShowCheckedModeBanner: false,
            theme: themesApp(context),
            routerConfig: AppRouter.router,
          ),
        );
      },
    );
  }
}
