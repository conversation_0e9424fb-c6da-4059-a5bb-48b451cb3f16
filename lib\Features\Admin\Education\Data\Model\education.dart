class Education {
  final String id;
  final String name;
  final String part;
  final String startDate;
  final String endDate;
  final String type; // 'education', 'experience', 'certification'
  final String? description;
  final String? location;
  final List<String>? technologies;

  Education({
    required this.id,
    required this.name,
    required this.part,
    required this.startDate,
    required this.endDate,
    required this.type,
    this.description,
    this.location,
    this.technologies,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'part': part,
      'startDate': startDate,
      'endDate': endDate,
      'type': type,
      'description': description,
      'location': location,
      'technologies': technologies,
    };
  }

  factory Education.fromJson(Map<String, dynamic> json) {
    return Education(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      part: json['part'] ?? '',
      startDate: json['startDate'] ?? '',
      endDate: json['endDate'] ?? '',
      type: json['type'] ?? 'education',
      description: json['description'],
      location: json['location'],
      technologies: json['technologies'] != null
          ? List<String>.from(json['technologies'])
          : null,
    );
  }

  Education copyWith({
    String? id,
    String? name,
    String? part,
    String? startDate,
    String? endDate,
    String? type,
    String? description,
    String? location,
    List<String>? technologies,
  }) {
    return Education(
      id: id ?? this.id,
      name: name ?? this.name,
      part: part ?? this.part,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      type: type ?? this.type,
      description: description ?? this.description,
      location: location ?? this.location,
      technologies: technologies ?? this.technologies,
    );
  }
}
