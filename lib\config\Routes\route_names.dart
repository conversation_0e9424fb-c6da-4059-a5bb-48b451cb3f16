class RouteNames {
  // Route paths
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboardWithUser = '/dashboard/:username';
  static const String directUsername = '/:username';

  // Route names (for GoRoute name parameter)
  static const String splashName = 'splash';
  static const String loginName = 'login';
  static const String registerName = 'register';
  static const String dashboardName = 'dashboard';
  static const String dashboardWithUserName = 'dashboardWithUser';
  static const String directUsernameName = 'directUsername';


  static const List<String> staticRoutes = [
    splash,
    login,
    register,
  ];

}
