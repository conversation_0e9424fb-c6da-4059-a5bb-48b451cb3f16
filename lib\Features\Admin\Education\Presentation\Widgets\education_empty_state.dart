import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationEmptyState extends StatelessWidget {
  const EducationEmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(30.w),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.textSecondary, 0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              Icons.school_outlined,
              size: 80.sp,
              color: AppColors.textSecondary,
            ),
          ),
          Sized<PERSON><PERSON>(height: 20.h),
          CustomText(
            text: AppStrings.noEducationYet,
            fontSize: ResponsiveLayout.getSubtitleFontSize(context),
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: 10.h),
          CustomText(
            text: AppStrings.addFirstEducation,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
            color: AppColors.textSecondary,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
