import 'package:devfolio/Core/models/portfolio_data_model.dart';
import 'package:devfolio/Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/login_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/register_page.dart';
import 'package:devfolio/Features/Portfolio/portfolio_main.dart';
import 'package:devfolio/config/Routes/app_routes.dart';
import 'package:devfolio/config/Routes/route_names.dart';
import 'package:go_router/go_router.dart';

/// GoRouter Configuration - Clean and Optimized
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.getInitialRoute(),
    navigatorKey: rootNavigatorKey,
    routes: [
      // Root route - redirects based on authentication
      GoRoute(
        path: "/",
        name: RouteNames.splash,
        redirect: (context, state) {
          if (state.fullPath == RouteNames.splash) {
            return AppRoutes.getInitialRoute();
          }
          return null;
        },
      ),

      // Static routes
      GoRoute(
        path: RouteNames.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: RouteNames.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      GoRoute(
        path: RouteNames.dashboard,
        name: 'dashboard',
        builder: (context, state) => const AdminDashboard(),
      ),
      GoRoute(
        path: RouteNames.portfolio,
        name: 'portfolio',
        builder: (context, state) {
          final extra = state.extra;
          String? email;
          PortfolioDataModel? portfolioData;

          if (extra is String) {
            email = extra;
          } else if (extra is PortfolioDataModel) {
            portfolioData = extra;
            email = portfolioData.email;
          }

          return PortfolioMain(email: email, portfolioData: portfolioData);
        },
      ),

      // Dynamic routes with username parameter
      GoRoute(
        path: RouteNames.dashboardWithUser,
        name: 'dashboardWithUser',
        builder: (context, state) {
          final username = state.pathParameters['username']!;
          if (AppRoutes.isCurrentUser(username)) {
            return const AdminDashboard();
          } else {
            return const LoginPage();
          }
        },
      ),
      GoRoute(
        path: RouteNames.portfolioWithUser,
        name: 'portfolioWithUser',
        builder: (context, state) {
          final extra = state.extra;
          String? email;
          PortfolioDataModel? portfolioData;

          if (extra is String) {
            email = extra;
          } else if (extra is PortfolioDataModel) {
            portfolioData = extra;
            email = portfolioData.email;
          }

          return PortfolioMain(email: email, portfolioData: portfolioData);
        },
      ),

      // Direct username routes
      GoRoute(
        path: RouteNames.directUsername,
        name: RouteNames.directUsername,
        builder: (context, state) {
          final username = state.pathParameters['username']!;

          // Check if it's a static route first
          if ([
            'login',
            'register',
            'dashboard',
            'portfolio',
          ].contains(username.toLowerCase())) {
            return const LoginPage();
          }

          // Handle username routes
          if (AppRoutes.isCurrentUser(username)) {
            return const AdminDashboard();
          } else {
            return const PortfolioMain();
          }
        },
      ),
    ],
    // Error handling - fallback to login
    errorBuilder: (context, state) => const LoginPage(),
  );
}
