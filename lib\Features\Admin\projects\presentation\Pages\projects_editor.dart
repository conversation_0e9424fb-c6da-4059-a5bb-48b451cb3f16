import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/project_model.dart';
import '../../../../../Core/resources/resources.dart';
import '../Cubit/projects_cubit.dart';
import '../Cubit/projects_state.dart';
import '../Widgets/projects_header.dart';
import '../Widgets/projects_empty_state.dart';
import '../Widgets/projects_list.dart';
import '../Widgets/project_form.dart';

class ProjectsEditor extends StatefulWidget {
  const ProjectsEditor({super.key});

  @override
  State<ProjectsEditor> createState() => _ProjectsEditorState();
}

class _ProjectsEditorState extends State<ProjectsEditor> {
  @override
  void initState() {
    super.initState();
    // Load projects data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProjectsCubit>().loadProjects();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(25.w),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProjectsHeader(
            onAddProject: () => context.read<ProjectsCubit>().addNewProject(),
          ),
          SizedBox(height: 25.h),
          Expanded(
            child: BlocConsumer<ProjectsCubit, ProjectsState>(
              listener: (context, state) {
                // Handle success and error messages here if needed
              },
              builder: (context, state) {
                if (state.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: AppColors.primary),
                  );
                }

                if (state.isAddingNew || state.editingProject != null) {
                  return _buildProjectForm(state);
                }

                if (state.projectsList.isEmpty) {
                  return ProjectsEmptyState(
                    onAddProject: () =>
                        context.read<ProjectsCubit>().addNewProject(),
                  );
                }

                return ProjectsList(
                  projects: state.projectsList,
                  onEdit: (project) =>
                      context.read<ProjectsCubit>().editProject(project),
                  onDelete: (project) => _showDeleteDialog(project),
                );
              },
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.1);
  }

  Widget _buildProjectForm(ProjectsState state) {
    return ProjectForm(
      project: state.editingProject,
      onSave: (project) {
        context.read<ProjectsCubit>().saveProject(
          title: project.title ?? '',
          description: project.description ?? '',
          image: project.image ?? '',
          githubUrl: project.github ?? '',
          liveUrl: project.liveUrl ?? '',
          category: project.category ?? '',
          technologies: project.technologies ?? [],
        );
      },
      onCancel: () => context.read<ProjectsCubit>().cancelEdit(),
    );
  }

  void _showDeleteDialog(ProjectModel project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.r),
        ),
        title: Row(
          children: [
            Icon(Icons.warning, color: AppColors.warning, size: 24.sp),
            SizedBox(width: 10.w),
            CustomText(
              text: AppStrings.deleteProject,
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),
          ],
        ),
        content: CustomText(
          text:
              '${AppStrings.deleteProjectConfirmation} "${project.title}"? ${AppStrings.actionCannotBeUndone}',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: CustomText(
              text: AppStrings.cancel,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.textSecondary,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<ProjectsCubit>().deleteProject(project);
            },
            child: CustomText(
              text: AppStrings.delete,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }
}
